#!/usr/bin/env python3
"""
测试难度度量功能的简单脚本
"""

import torch
import sys
import os

# 添加路径
sys.path.append('/data/wuyang/R1-Omni-main/src/r1-v/src')

def test_difficulty_methods():
    """测试难度度量相关方法"""
    
    # 模拟一个简化的trainer实例
    class MockTrainer:
        def __init__(self):
            # 模拟tokenizer
            class MockTokenizer:
                def encode(self, text, add_special_tokens=False):
                    # 简单的mock编码
                    return [1, 2, 3] if text else []
                
                def decode(self, tokens, skip_special_tokens=False):
                    # 简单的mock解码
                    if isinstance(tokens, torch.Tensor):
                        tokens = tokens.tolist()
                    if tokens == [1, 2, 3]:
                        return "test <answer>happy</answer> content"
                    elif tokens == [4, 5, 6]:
                        return "test <answer>sad</answer> content"
                    else:
                        return "no answer content"
            
            class MockProcessingClass:
                def __init__(self):
                    self.tokenizer = MockTokenizer()
            
            self.processing_class = MockProcessingClass()
        
        def _extract_last_answer(self, text):
            """提取最后一个answer标签的内容"""
            import re
            pattern = r'<answer>\s*(.*?)\s*</answer>'
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
            
            if matches:
                return matches[-1].strip()  # 取最后一个
            return None
        
        def _locate_content_in_sequence(self, answer_content, seq):
            """定位answer内容在token序列中的位置"""
            if not answer_content:
                return torch.zeros_like(seq, dtype=torch.bool)
            
            # 简化的实现：假设answer内容总是在位置2-4
            mask = torch.zeros_like(seq, dtype=torch.bool, device=seq.device)
            if len(seq) > 4:
                mask[2:5] = True
            return mask
        
        def _compute_answer_confidence(self, per_token_logps, completion_ids, completion_mask):
            """计算answer标签内容的置信度"""
            confidences = []
            
            for logps, seq, comp_mask in zip(per_token_logps, completion_ids, completion_mask):
                text = self.processing_class.tokenizer.decode(seq, skip_special_tokens=False)
                answer_content = self._extract_last_answer(text)
                
                if answer_content is not None:
                    answer_mask = self._locate_content_in_sequence(answer_content, seq)
                    valid_mask = answer_mask & comp_mask
                    
                    if valid_mask.sum() > 0:
                        answer_logps = logps[valid_mask]
                        confidence = torch.exp(answer_logps.mean())
                    else:
                        confidence = torch.tensor(0.0)
                else:
                    confidence = torch.tensor(0.0)
                    
                confidences.append(confidence)
            
            return torch.stack(confidences)
        
        def _detect_modal_conflict(self, visual_completion_ids, audio_completion_ids):
            """检测模态冲突：比较answer文本内容"""
            conflicts = []
            
            for visual_seq, audio_seq in zip(visual_completion_ids, audio_completion_ids):
                visual_text = self.processing_class.tokenizer.decode(visual_seq, skip_special_tokens=False)
                audio_text = self.processing_class.tokenizer.decode(audio_seq, skip_special_tokens=False)
                
                visual_answer = self._extract_last_answer(visual_text)
                audio_answer = self._extract_last_answer(audio_text)
                
                if visual_answer is not None and audio_answer is not None:
                    conflict = (visual_answer.strip().lower() != audio_answer.strip().lower())
                else:
                    conflict = True  # 格式错误认为是冲突
                    
                conflicts.append(conflict)
            
            return torch.tensor(conflicts, dtype=torch.bool)
        
        def _detect_confidence_anomaly(self, conf_full, conf_visual, conf_audio, threshold=0.1):
            """检测置信度异常提升"""
            visual_boost = conf_visual - conf_full
            audio_boost = conf_audio - conf_full
            
            return {
                'visual_anomaly': visual_boost > threshold,
                'audio_anomaly': audio_boost > threshold,
                'visual_boost': visual_boost,
                'audio_boost': audio_boost
            }
        
        def _compute_difficulty_score_binary(self, modal_conflict, visual_anomaly, audio_anomaly):
            """二值难度判断：easy(0) 或 hard(1)"""
            difficulty_scores = []
            
            for conflict, v_anomaly, a_anomaly in zip(modal_conflict, visual_anomaly, audio_anomaly):
                # 只要满足任一条件就是hard
                if conflict or v_anomaly or a_anomaly:
                    score = 1  # hard
                else:
                    score = 0  # easy
                    
                difficulty_scores.append(score)
            
            return torch.tensor(difficulty_scores, dtype=torch.int)
    
    # 创建测试实例
    trainer = MockTrainer()
    
    # 测试数据
    batch_size = 2
    seq_len = 10
    
    # 模拟数据
    per_token_logps = torch.randn(batch_size, seq_len)
    completion_ids = torch.tensor([[1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 
                                   [4, 5, 6, 7, 8, 9, 10, 11, 12, 13]])
    completion_mask = torch.ones(batch_size, seq_len, dtype=torch.bool)
    
    visual_ids = torch.tensor([[1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 
                               [4, 5, 6, 7, 8, 9, 10, 11, 12, 13]])
    audio_ids = torch.tensor([[4, 5, 6, 7, 8, 9, 10, 11, 12, 13], 
                              [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]])
    
    print("开始测试难度度量功能...")
    
    # 测试置信度计算
    print("\n1. 测试置信度计算")
    confidences = trainer._compute_answer_confidence(per_token_logps, completion_ids, completion_mask)
    print(f"置信度: {confidences}")
    
    # 测试模态冲突检测
    print("\n2. 测试模态冲突检测")
    conflicts = trainer._detect_modal_conflict(visual_ids, audio_ids)
    print(f"模态冲突: {conflicts}")
    
    # 测试置信度异常检测
    print("\n3. 测试置信度异常检测")
    conf_full = torch.tensor([0.5, 0.6])
    conf_visual = torch.tensor([0.7, 0.5])  # 第一个有异常提升
    conf_audio = torch.tensor([0.4, 0.8])   # 第二个有异常提升
    
    anomaly_results = trainer._detect_confidence_anomaly(conf_full, conf_visual, conf_audio)
    print(f"视觉异常: {anomaly_results['visual_anomaly']}")
    print(f"音频异常: {anomaly_results['audio_anomaly']}")
    print(f"视觉提升: {anomaly_results['visual_boost']}")
    print(f"音频提升: {anomaly_results['audio_boost']}")
    
    # 测试难度分数计算
    print("\n4. 测试难度分数计算")
    difficulty_scores = trainer._compute_difficulty_score_binary(
        conflicts, 
        anomaly_results['visual_anomaly'], 
        anomaly_results['audio_anomaly']
    )
    print(f"难度分数: {difficulty_scores}")
    
    print("\n测试完成！")
    
    return True

if __name__ == "__main__":
    test_difficulty_methods()

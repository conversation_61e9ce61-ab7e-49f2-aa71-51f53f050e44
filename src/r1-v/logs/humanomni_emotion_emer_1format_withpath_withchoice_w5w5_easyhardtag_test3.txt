------------- 12-17-10-23-023335 New Length Reward Debug -------------
Group Accuracy: 0.167 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 926.1666666666666
Completion 0: length=700, L/Lavg=0.756, ra=0.0, reward=-0.244
Completion 1: length=1078, L/Lavg=1.164, ra=1.0, reward=0.000
Completion 2: length=723, L/Lavg=0.781, ra=0.0, reward=-0.219
Completion 3: length=1244, L/Lavg=1.343, ra=0.0, reward=0.343
Completion 4: length=685, L/Lavg=0.740, ra=0.0, reward=-0.260
Completion 5: length=1127, L/Lavg=1.217, ra=0.0, reward=0.217
------------- 12-17-10-23-028914 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 793.0
Completion 0: length=700, L/Lavg=0.883, ra=0.0, reward=-0.117
Completion 1: length=1063, L/Lavg=1.340, ra=0.0, reward=0.340
Completion 2: length=1158, L/Lavg=1.460, ra=0.0, reward=0.460
Completion 3: length=730, L/Lavg=0.921, ra=0.0, reward=-0.079
Completion 4: length=504, L/Lavg=0.636, ra=0.0, reward=-0.364
Completion 5: length=603, L/Lavg=0.760, ra=0.0, reward=-0.240
------------- 12-17-10-52-434651 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 915.1666666666666
Completion 0: length=959, L/Lavg=1.048, ra=0.0, reward=0.048
Completion 1: length=525, L/Lavg=0.574, ra=0.0, reward=-0.426
Completion 2: length=594, L/Lavg=0.649, ra=0.0, reward=-0.351
Completion 3: length=1808, L/Lavg=1.976, ra=0.0, reward=0.976
Completion 4: length=980, L/Lavg=1.071, ra=0.0, reward=0.071
Completion 5: length=625, L/Lavg=0.683, ra=0.0, reward=-0.317
------------- 12-17-10-52-799592 New Length Reward Debug -------------
Group Accuracy: 0.333 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1035.1666666666667
Completion 0: length=728, L/Lavg=0.703, ra=1.0, reward=0.000
Completion 1: length=1790, L/Lavg=1.729, ra=0.0, reward=0.729
Completion 2: length=1001, L/Lavg=0.967, ra=0.0, reward=-0.033
Completion 3: length=1171, L/Lavg=1.131, ra=0.0, reward=0.131
Completion 4: length=899, L/Lavg=0.868, ra=0.0, reward=-0.132
Completion 5: length=622, L/Lavg=0.601, ra=1.0, reward=0.000
------------- 12-17-12-24-985813 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 992.5
Completion 0: length=731, L/Lavg=0.737, ra=0.0, reward=-0.263
Completion 1: length=941, L/Lavg=0.948, ra=1.0, reward=0.000
Completion 2: length=568, L/Lavg=0.572, ra=0.0, reward=-0.428
Completion 3: length=1321, L/Lavg=1.331, ra=1.0, reward=0.000
Completion 4: length=564, L/Lavg=0.568, ra=1.0, reward=0.000
Completion 5: length=1830, L/Lavg=1.844, ra=0.0, reward=0.844
------------- 12-17-12-24-986024 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 793.0
Completion 0: length=700, L/Lavg=0.883, ra=0.0, reward=-0.117
Completion 1: length=1063, L/Lavg=1.340, ra=0.0, reward=0.340
Completion 2: length=1158, L/Lavg=1.460, ra=0.0, reward=0.460
Completion 3: length=730, L/Lavg=0.921, ra=0.0, reward=-0.079
Completion 4: length=504, L/Lavg=0.636, ra=0.0, reward=-0.364
Completion 5: length=603, L/Lavg=0.760, ra=0.0, reward=-0.240
------------- 12-17-12-24-986724 New Length Reward Debug -------------
Group Accuracy: 0.167 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 926.1666666666666
Completion 0: length=700, L/Lavg=0.756, ra=0.0, reward=-0.244
Completion 1: length=1078, L/Lavg=1.164, ra=1.0, reward=0.000
Completion 2: length=723, L/Lavg=0.781, ra=0.0, reward=-0.219
Completion 3: length=1244, L/Lavg=1.343, ra=0.0, reward=0.343
Completion 4: length=685, L/Lavg=0.740, ra=0.0, reward=-0.260
Completion 5: length=1127, L/Lavg=1.217, ra=0.0, reward=0.217
------------- 12-17-12-24-988923 New Length Reward Debug -------------
Group Accuracy: 0.167 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 931.0
Completion 0: length=985, L/Lavg=1.058, ra=0.0, reward=0.058
Completion 1: length=620, L/Lavg=0.666, ra=0.0, reward=-0.334
Completion 2: length=673, L/Lavg=0.723, ra=0.0, reward=-0.277
Completion 3: length=1050, L/Lavg=1.128, ra=1.0, reward=0.000
Completion 4: length=1067, L/Lavg=1.146, ra=0.0, reward=0.146
Completion 5: length=1191, L/Lavg=1.279, ra=0.0, reward=0.279
------------- 12-17-12-53-913012 New Length Reward Debug -------------
Group Accuracy: 0.333 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 808.8333333333334
Completion 0: length=1061, L/Lavg=1.312, ra=1.0, reward=0.000
Completion 1: length=797, L/Lavg=0.985, ra=1.0, reward=0.000
Completion 2: length=807, L/Lavg=0.998, ra=0.0, reward=-0.002
Completion 3: length=805, L/Lavg=0.995, ra=0.0, reward=-0.005
Completion 4: length=486, L/Lavg=0.601, ra=0.0, reward=-0.399
Completion 5: length=897, L/Lavg=1.109, ra=0.0, reward=0.109
------------- 12-17-12-53-987386 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 817.8333333333334
Completion 0: length=611, L/Lavg=0.747, ra=0.0, reward=-0.253
Completion 1: length=1109, L/Lavg=1.356, ra=0.0, reward=0.356
Completion 2: length=1308, L/Lavg=1.599, ra=0.0, reward=0.599
Completion 3: length=575, L/Lavg=0.703, ra=0.0, reward=-0.297
Completion 4: length=675, L/Lavg=0.825, ra=0.0, reward=-0.175
Completion 5: length=629, L/Lavg=0.769, ra=0.0, reward=-0.231
------------- 12-17-12-54-080717 New Length Reward Debug -------------
Group Accuracy: 0.333 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 696.6666666666666
Completion 0: length=719, L/Lavg=1.032, ra=0.0, reward=0.032
Completion 1: length=844, L/Lavg=1.211, ra=0.0, reward=0.211
Completion 2: length=646, L/Lavg=0.927, ra=1.0, reward=0.000
Completion 3: length=696, L/Lavg=0.999, ra=1.0, reward=0.000
Completion 4: length=847, L/Lavg=1.216, ra=0.0, reward=0.216
Completion 5: length=428, L/Lavg=0.614, ra=0.0, reward=-0.386
------------- 12-17-12-54-115031 New Length Reward Debug -------------
Group Accuracy: 1.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 1053.3333333333333
Completion 0: length=1404, L/Lavg=1.333, ra=1.0, reward=-0.333
Completion 1: length=914, L/Lavg=0.868, ra=1.0, reward=0.132
Completion 2: length=899, L/Lavg=0.853, ra=1.0, reward=0.147
Completion 3: length=1221, L/Lavg=1.159, ra=1.0, reward=-0.159
Completion 4: length=810, L/Lavg=0.769, ra=1.0, reward=0.231
Completion 5: length=1072, L/Lavg=1.018, ra=1.0, reward=-0.018

------------- 12-17-10-23-023335 New Length Reward Debug -------------
Group Accuracy: 0.167 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 926.1666666666666
Completion 0: length=700, L/Lavg=0.756, ra=0.0, reward=-0.244
Completion 1: length=1078, L/Lavg=1.164, ra=1.0, reward=0.000
Completion 2: length=723, L/Lavg=0.781, ra=0.0, reward=-0.219
Completion 3: length=1244, L/Lavg=1.343, ra=0.0, reward=0.343
Completion 4: length=685, L/Lavg=0.740, ra=0.0, reward=-0.260
Completion 5: length=1127, L/Lavg=1.217, ra=0.0, reward=0.217
------------- 12-17-10-23-028914 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 793.0
Completion 0: length=700, L/Lavg=0.883, ra=0.0, reward=-0.117
Completion 1: length=1063, L/Lavg=1.340, ra=0.0, reward=0.340
Completion 2: length=1158, L/Lavg=1.460, ra=0.0, reward=0.460
Completion 3: length=730, L/Lavg=0.921, ra=0.0, reward=-0.079
Completion 4: length=504, L/Lavg=0.636, ra=0.0, reward=-0.364
Completion 5: length=603, L/Lavg=0.760, ra=0.0, reward=-0.240
------------- 12-17-10-52-434651 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 915.1666666666666
Completion 0: length=959, L/Lavg=1.048, ra=0.0, reward=0.048
Completion 1: length=525, L/Lavg=0.574, ra=0.0, reward=-0.426
Completion 2: length=594, L/Lavg=0.649, ra=0.0, reward=-0.351
Completion 3: length=1808, L/Lavg=1.976, ra=0.0, reward=0.976
Completion 4: length=980, L/Lavg=1.071, ra=0.0, reward=0.071
Completion 5: length=625, L/Lavg=0.683, ra=0.0, reward=-0.317
------------- 12-17-10-52-799592 New Length Reward Debug -------------
Group Accuracy: 0.333 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1035.1666666666667
Completion 0: length=728, L/Lavg=0.703, ra=1.0, reward=0.000
Completion 1: length=1790, L/Lavg=1.729, ra=0.0, reward=0.729
Completion 2: length=1001, L/Lavg=0.967, ra=0.0, reward=-0.033
Completion 3: length=1171, L/Lavg=1.131, ra=0.0, reward=0.131
Completion 4: length=899, L/Lavg=0.868, ra=0.0, reward=-0.132
Completion 5: length=622, L/Lavg=0.601, ra=1.0, reward=0.000
------------- 12-17-12-24-985813 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 992.5
Completion 0: length=731, L/Lavg=0.737, ra=0.0, reward=-0.263
Completion 1: length=941, L/Lavg=0.948, ra=1.0, reward=0.000
Completion 2: length=568, L/Lavg=0.572, ra=0.0, reward=-0.428
Completion 3: length=1321, L/Lavg=1.331, ra=1.0, reward=0.000
Completion 4: length=564, L/Lavg=0.568, ra=1.0, reward=0.000
Completion 5: length=1830, L/Lavg=1.844, ra=0.0, reward=0.844
------------- 12-17-12-24-986024 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 793.0
Completion 0: length=700, L/Lavg=0.883, ra=0.0, reward=-0.117
Completion 1: length=1063, L/Lavg=1.340, ra=0.0, reward=0.340
Completion 2: length=1158, L/Lavg=1.460, ra=0.0, reward=0.460
Completion 3: length=730, L/Lavg=0.921, ra=0.0, reward=-0.079
Completion 4: length=504, L/Lavg=0.636, ra=0.0, reward=-0.364
Completion 5: length=603, L/Lavg=0.760, ra=0.0, reward=-0.240
------------- 12-17-12-24-986724 New Length Reward Debug -------------
Group Accuracy: 0.167 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 926.1666666666666
Completion 0: length=700, L/Lavg=0.756, ra=0.0, reward=-0.244
Completion 1: length=1078, L/Lavg=1.164, ra=1.0, reward=0.000
Completion 2: length=723, L/Lavg=0.781, ra=0.0, reward=-0.219
Completion 3: length=1244, L/Lavg=1.343, ra=0.0, reward=0.343
Completion 4: length=685, L/Lavg=0.740, ra=0.0, reward=-0.260
Completion 5: length=1127, L/Lavg=1.217, ra=0.0, reward=0.217
------------- 12-17-12-24-988923 New Length Reward Debug -------------
Group Accuracy: 0.167 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 931.0
Completion 0: length=985, L/Lavg=1.058, ra=0.0, reward=0.058
Completion 1: length=620, L/Lavg=0.666, ra=0.0, reward=-0.334
Completion 2: length=673, L/Lavg=0.723, ra=0.0, reward=-0.277
Completion 3: length=1050, L/Lavg=1.128, ra=1.0, reward=0.000
Completion 4: length=1067, L/Lavg=1.146, ra=0.0, reward=0.146
Completion 5: length=1191, L/Lavg=1.279, ra=0.0, reward=0.279
------------- 12-17-12-53-913012 New Length Reward Debug -------------
Group Accuracy: 0.333 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 808.8333333333334
Completion 0: length=1061, L/Lavg=1.312, ra=1.0, reward=0.000
Completion 1: length=797, L/Lavg=0.985, ra=1.0, reward=0.000
Completion 2: length=807, L/Lavg=0.998, ra=0.0, reward=-0.002
Completion 3: length=805, L/Lavg=0.995, ra=0.0, reward=-0.005
Completion 4: length=486, L/Lavg=0.601, ra=0.0, reward=-0.399
Completion 5: length=897, L/Lavg=1.109, ra=0.0, reward=0.109
------------- 12-17-12-53-987386 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 817.8333333333334
Completion 0: length=611, L/Lavg=0.747, ra=0.0, reward=-0.253
Completion 1: length=1109, L/Lavg=1.356, ra=0.0, reward=0.356
Completion 2: length=1308, L/Lavg=1.599, ra=0.0, reward=0.599
Completion 3: length=575, L/Lavg=0.703, ra=0.0, reward=-0.297
Completion 4: length=675, L/Lavg=0.825, ra=0.0, reward=-0.175
Completion 5: length=629, L/Lavg=0.769, ra=0.0, reward=-0.231
------------- 12-17-12-54-080717 New Length Reward Debug -------------
Group Accuracy: 0.333 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 696.6666666666666
Completion 0: length=719, L/Lavg=1.032, ra=0.0, reward=0.032
Completion 1: length=844, L/Lavg=1.211, ra=0.0, reward=0.211
Completion 2: length=646, L/Lavg=0.927, ra=1.0, reward=0.000
Completion 3: length=696, L/Lavg=0.999, ra=1.0, reward=0.000
Completion 4: length=847, L/Lavg=1.216, ra=0.0, reward=0.216
Completion 5: length=428, L/Lavg=0.614, ra=0.0, reward=-0.386
------------- 12-17-12-54-115031 New Length Reward Debug -------------
Group Accuracy: 1.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 1053.3333333333333
Completion 0: length=1404, L/Lavg=1.333, ra=1.0, reward=-0.333
Completion 1: length=914, L/Lavg=0.868, ra=1.0, reward=0.132
Completion 2: length=899, L/Lavg=0.853, ra=1.0, reward=0.147
Completion 3: length=1221, L/Lavg=1.159, ra=1.0, reward=-0.159
Completion 4: length=810, L/Lavg=0.769, ra=1.0, reward=0.231
Completion 5: length=1072, L/Lavg=1.018, ra=1.0, reward=-0.018
------------- 12-17-21-59-588936 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 793.0
Completion 0: length=700, L/Lavg=0.883, ra=0.0, reward=-0.117
Completion 1: length=1063, L/Lavg=1.340, ra=0.0, reward=0.340
Completion 2: length=1158, L/Lavg=1.460, ra=0.0, reward=0.460
Completion 3: length=730, L/Lavg=0.921, ra=0.0, reward=-0.079
Completion 4: length=504, L/Lavg=0.636, ra=0.0, reward=-0.364
Completion 5: length=603, L/Lavg=0.760, ra=0.0, reward=-0.240
------------- 12-17-21-59-589147 New Length Reward Debug -------------
Group Accuracy: 0.167 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 926.1666666666666
Completion 0: length=700, L/Lavg=0.756, ra=0.0, reward=-0.244
Completion 1: length=1078, L/Lavg=1.164, ra=1.0, reward=0.000
Completion 2: length=723, L/Lavg=0.781, ra=0.0, reward=-0.219
Completion 3: length=1244, L/Lavg=1.343, ra=0.0, reward=0.343
Completion 4: length=685, L/Lavg=0.740, ra=0.0, reward=-0.260
Completion 5: length=1127, L/Lavg=1.217, ra=0.0, reward=0.217
------------- 12-17-22-26-163901 New Length Reward Debug -------------
Group Accuracy: 0.333 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1035.1666666666667
Completion 0: length=728, L/Lavg=0.703, ra=1.0, reward=0.000
Completion 1: length=1790, L/Lavg=1.729, ra=0.0, reward=0.729
Completion 2: length=1001, L/Lavg=0.967, ra=0.0, reward=-0.033
Completion 3: length=1171, L/Lavg=1.131, ra=0.0, reward=0.131
Completion 4: length=899, L/Lavg=0.868, ra=0.0, reward=-0.132
Completion 5: length=622, L/Lavg=0.601, ra=1.0, reward=0.000
------------- 12-17-22-26-283985 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 915.1666666666666
Completion 0: length=959, L/Lavg=1.048, ra=0.0, reward=0.048
Completion 1: length=525, L/Lavg=0.574, ra=0.0, reward=-0.426
Completion 2: length=594, L/Lavg=0.649, ra=0.0, reward=-0.351
Completion 3: length=1808, L/Lavg=1.976, ra=0.0, reward=0.976
Completion 4: length=980, L/Lavg=1.071, ra=0.0, reward=0.071
Completion 5: length=625, L/Lavg=0.683, ra=0.0, reward=-0.317
------------- 12-17-26-26-848562 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=-0.117
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.091
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.056
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=-0.030
------------- 12-17-26-26-850000 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=-0.392
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.311
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.117
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.000
------------- 12-17-26-48-458683 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 960.75
Completion 0: length=1054, L/Lavg=1.097, ra=0.0, reward=0.097
Completion 1: length=595, L/Lavg=0.619, ra=0.0, reward=-0.381
Completion 2: length=1085, L/Lavg=1.129, ra=0.0, reward=0.129
Completion 3: length=1109, L/Lavg=1.154, ra=0.0, reward=0.154
------------- 12-17-26-48-512347 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 991.25
Completion 0: length=960, L/Lavg=0.968, ra=1.0, reward=0.000
Completion 1: length=1433, L/Lavg=1.446, ra=1.0, reward=0.000
Completion 2: length=766, L/Lavg=0.773, ra=0.0, reward=-0.227
Completion 3: length=806, L/Lavg=0.813, ra=0.0, reward=-0.187
------------- 12-17-27-05-303603 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 923.5
Completion 0: length=1307, L/Lavg=1.415, ra=0.0, reward=0.415
Completion 1: length=837, L/Lavg=0.906, ra=0.0, reward=-0.094
Completion 2: length=554, L/Lavg=0.600, ra=1.0, reward=0.000
Completion 3: length=996, L/Lavg=1.079, ra=0.0, reward=0.079
------------- 12-17-27-05-347083 New Length Reward Debug -------------
Group Accuracy: 1.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 1019.5
Completion 0: length=703, L/Lavg=0.690, ra=1.0, reward=0.310
Completion 1: length=1167, L/Lavg=1.145, ra=1.0, reward=-0.145
Completion 2: length=1450, L/Lavg=1.422, ra=1.0, reward=-0.422
Completion 3: length=758, L/Lavg=0.744, ra=1.0, reward=0.256
------------- 12-17-27-22-545978 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 813.75
Completion 0: length=788, L/Lavg=0.968, ra=0.0, reward=-0.032
Completion 1: length=489, L/Lavg=0.601, ra=0.0, reward=-0.399
Completion 2: length=1236, L/Lavg=1.519, ra=1.0, reward=0.000
Completion 3: length=742, L/Lavg=0.912, ra=0.0, reward=-0.088
------------- 12-17-27-22-596889 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 646.25
Completion 0: length=770, L/Lavg=1.191, ra=0.0, reward=0.191
Completion 1: length=797, L/Lavg=1.233, ra=0.0, reward=0.233
Completion 2: length=736, L/Lavg=1.139, ra=0.0, reward=0.139
Completion 3: length=282, L/Lavg=0.436, ra=0.0, reward=-0.564
------------- 12-17-27-37-088987 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 714.5
Completion 0: length=692, L/Lavg=0.969, ra=0.0, reward=-0.031
Completion 1: length=653, L/Lavg=0.914, ra=1.0, reward=0.000
Completion 2: length=734, L/Lavg=1.027, ra=0.0, reward=0.027
Completion 3: length=779, L/Lavg=1.090, ra=0.0, reward=0.090
------------- 12-17-27-37-134146 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 890.5
Completion 0: length=1321, L/Lavg=1.483, ra=0.0, reward=0.000
Completion 1: length=358, L/Lavg=0.402, ra=1.0, reward=0.598
Completion 2: length=765, L/Lavg=0.859, ra=1.0, reward=0.141
Completion 3: length=1118, L/Lavg=1.255, ra=1.0, reward=-0.255
------------- 12-17-27-57-918833 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 686.75
Completion 0: length=831, L/Lavg=1.210, ra=1.0, reward=0.000
Completion 1: length=512, L/Lavg=0.746, ra=0.0, reward=-0.254
Completion 2: length=598, L/Lavg=0.871, ra=0.0, reward=-0.129
Completion 3: length=806, L/Lavg=1.174, ra=1.0, reward=0.000
------------- 12-17-27-57-952844 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 999.5
Completion 0: length=556, L/Lavg=0.556, ra=0.0, reward=-0.444
Completion 1: length=1502, L/Lavg=1.503, ra=0.0, reward=0.503
Completion 2: length=1049, L/Lavg=1.050, ra=0.0, reward=0.050
Completion 3: length=891, L/Lavg=0.891, ra=0.0, reward=-0.109
------------- 12-17-28-14-476580 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 875.0
Completion 0: length=617, L/Lavg=0.705, ra=1.0, reward=0.295
Completion 1: length=943, L/Lavg=1.078, ra=1.0, reward=-0.078
Completion 2: length=736, L/Lavg=0.841, ra=1.0, reward=0.159
Completion 3: length=1204, L/Lavg=1.376, ra=0.0, reward=0.000
------------- 12-17-28-14-503554 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 832.75
Completion 0: length=1061, L/Lavg=1.274, ra=1.0, reward=0.000
Completion 1: length=733, L/Lavg=0.880, ra=0.0, reward=-0.120
Completion 2: length=840, L/Lavg=1.009, ra=0.0, reward=0.009
Completion 3: length=697, L/Lavg=0.837, ra=0.0, reward=-0.163
------------- 12-17-51-23-830845 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=-0.117
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.091
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.056
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=-0.030
------------- 12-17-51-23-831160 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=-0.392
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.311
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.117
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.000
------------- 12-17-51-45-395374 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 960.75
Completion 0: length=1054, L/Lavg=1.097, ra=0.0, reward=0.097
Completion 1: length=595, L/Lavg=0.619, ra=0.0, reward=-0.381
Completion 2: length=1085, L/Lavg=1.129, ra=0.0, reward=0.129
Completion 3: length=1109, L/Lavg=1.154, ra=0.0, reward=0.154
------------- 12-17-51-45-438847 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 991.25
Completion 0: length=960, L/Lavg=0.968, ra=1.0, reward=0.000
Completion 1: length=1433, L/Lavg=1.446, ra=1.0, reward=0.000
Completion 2: length=766, L/Lavg=0.773, ra=0.0, reward=-0.227
Completion 3: length=806, L/Lavg=0.813, ra=0.0, reward=-0.187
------------- 12-17-52-01-801148 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 1038.75
Completion 0: length=1745, L/Lavg=1.680, ra=1.0, reward=-0.680
Completion 1: length=664, L/Lavg=0.639, ra=0.0, reward=0.000
Completion 2: length=779, L/Lavg=0.750, ra=1.0, reward=0.250
Completion 3: length=967, L/Lavg=0.931, ra=1.0, reward=0.069
------------- 12-17-52-01-822611 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1047.75
Completion 0: length=1695, L/Lavg=1.618, ra=0.0, reward=0.618
Completion 1: length=604, L/Lavg=0.576, ra=0.0, reward=-0.424
Completion 2: length=745, L/Lavg=0.711, ra=1.0, reward=0.000
Completion 3: length=1147, L/Lavg=1.095, ra=0.0, reward=0.095
------------- 12-17-52-15-497870 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 676.75
Completion 0: length=667, L/Lavg=0.986, ra=0.0, reward=-0.014
Completion 1: length=625, L/Lavg=0.924, ra=0.0, reward=-0.076
Completion 2: length=880, L/Lavg=1.300, ra=0.0, reward=0.300
Completion 3: length=535, L/Lavg=0.791, ra=0.0, reward=-0.209
------------- 12-17-52-15-515760 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 656.25
Completion 0: length=702, L/Lavg=1.070, ra=0.0, reward=0.070
Completion 1: length=590, L/Lavg=0.899, ra=1.0, reward=0.000
Completion 2: length=801, L/Lavg=1.221, ra=0.0, reward=0.221
Completion 3: length=532, L/Lavg=0.811, ra=0.0, reward=-0.189
------------- 12-17-52-33-263940 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1170.5
Completion 0: length=1208, L/Lavg=1.032, ra=0.0, reward=0.032
Completion 1: length=1993, L/Lavg=1.703, ra=0.0, reward=0.703
Completion 2: length=678, L/Lavg=0.579, ra=1.0, reward=0.000
Completion 3: length=803, L/Lavg=0.686, ra=0.0, reward=-0.314
------------- 12-17-52-33-288400 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 612.0
Completion 0: length=790, L/Lavg=1.291, ra=0.0, reward=0.291
Completion 1: length=498, L/Lavg=0.814, ra=0.0, reward=-0.186
Completion 2: length=437, L/Lavg=0.714, ra=0.0, reward=-0.286
Completion 3: length=723, L/Lavg=1.181, ra=1.0, reward=0.000
------------- 12-17-52-48-107274 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 1060.5
Completion 0: length=744, L/Lavg=0.702, ra=0.0, reward=-0.298
Completion 1: length=1446, L/Lavg=1.364, ra=0.0, reward=0.364
Completion 2: length=1046, L/Lavg=0.986, ra=1.0, reward=0.000
Completion 3: length=1006, L/Lavg=0.949, ra=0.0, reward=-0.051
------------- 12-17-52-48-133591 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 665.5
Completion 0: length=747, L/Lavg=1.122, ra=0.0, reward=0.122
Completion 1: length=531, L/Lavg=0.798, ra=0.0, reward=-0.202
Completion 2: length=472, L/Lavg=0.709, ra=0.0, reward=-0.291
Completion 3: length=912, L/Lavg=1.370, ra=0.0, reward=0.370
------------- 12-17-53-05-639610 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 698.0
Completion 0: length=784, L/Lavg=1.123, ra=0.0, reward=0.123
Completion 1: length=670, L/Lavg=0.960, ra=0.0, reward=-0.040
Completion 2: length=540, L/Lavg=0.774, ra=1.0, reward=0.000
Completion 3: length=798, L/Lavg=1.143, ra=1.0, reward=0.000
------------- 12-17-53-05-657702 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 816.0
Completion 0: length=868, L/Lavg=1.064, ra=1.0, reward=0.000
Completion 1: length=898, L/Lavg=1.100, ra=1.0, reward=0.000
Completion 2: length=823, L/Lavg=1.009, ra=0.0, reward=0.009
Completion 3: length=675, L/Lavg=0.827, ra=0.0, reward=-0.173
------------- 12-17-55-17-964627 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=-0.392
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.311
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.117
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.000
------------- 12-17-55-17-965549 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=-0.117
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.091
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.056
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=-0.030
------------- 12-17-55-39-491738 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 991.25
Completion 0: length=960, L/Lavg=0.968, ra=1.0, reward=0.000
Completion 1: length=1433, L/Lavg=1.446, ra=1.0, reward=0.000
Completion 2: length=766, L/Lavg=0.773, ra=0.0, reward=-0.227
Completion 3: length=806, L/Lavg=0.813, ra=0.0, reward=-0.187
------------- 12-17-55-39-710252 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 960.75
Completion 0: length=1054, L/Lavg=1.097, ra=0.0, reward=0.097
Completion 1: length=595, L/Lavg=0.619, ra=0.0, reward=-0.381
Completion 2: length=1085, L/Lavg=1.129, ra=0.0, reward=0.129
Completion 3: length=1109, L/Lavg=1.154, ra=0.0, reward=0.154
------------- 12-17-55-55-807813 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 1198.5
Completion 0: length=1382, L/Lavg=1.153, ra=1.0, reward=-0.153
Completion 1: length=665, L/Lavg=0.555, ra=0.0, reward=0.000
Completion 2: length=1590, L/Lavg=1.327, ra=1.0, reward=-0.327
Completion 3: length=1157, L/Lavg=0.965, ra=1.0, reward=0.035
------------- 12-17-55-55-837513 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 826.25
Completion 0: length=1062, L/Lavg=1.285, ra=1.0, reward=0.000
Completion 1: length=618, L/Lavg=0.748, ra=0.0, reward=-0.252
Completion 2: length=482, L/Lavg=0.583, ra=0.0, reward=-0.417
Completion 3: length=1143, L/Lavg=1.383, ra=0.0, reward=0.383
------------- 12-17-56-12-339279 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 772.25
Completion 0: length=563, L/Lavg=0.729, ra=0.0, reward=-0.271
Completion 1: length=776, L/Lavg=1.005, ra=0.0, reward=0.005
Completion 2: length=1241, L/Lavg=1.607, ra=0.0, reward=0.607
Completion 3: length=509, L/Lavg=0.659, ra=0.0, reward=-0.341
------------- 12-17-56-12-588208 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 904.25
Completion 0: length=1456, L/Lavg=1.610, ra=0.0, reward=0.610
Completion 1: length=897, L/Lavg=0.992, ra=0.0, reward=-0.008
Completion 2: length=598, L/Lavg=0.661, ra=0.0, reward=-0.339
Completion 3: length=666, L/Lavg=0.737, ra=0.0, reward=-0.263
------------- 12-18-09-44-424127 Length Reward Debug -------------
Difficulty Score: tensor([1], device='cuda:1', dtype=torch.int32) (0=Easy, 1=Hard)
Problem Type: Hard
Modal Conflict: tensor([False], device='cuda:1')
Visual Anomaly: tensor([True], device='cuda:1')
Audio Anomaly: tensor([True], device='cuda:1')
Full Modal Confidence: tensor([0.7500], device='cuda:1', grad_fn=<MeanBackward1>)
Visual Only Confidence: tensor([1.], device='cuda:1', dtype=torch.bfloat16, grad_fn=<StackBackward0>)
Audio Only Confidence: tensor([1.], device='cuda:1', dtype=torch.bfloat16, grad_fn=<StackBackward0>)
------------- 12-18-09-44-425524 Length Reward Debug -------------
Difficulty Score: tensor([1], device='cuda:3', dtype=torch.int32) (0=Easy, 1=Hard)
Problem Type: Hard
Modal Conflict: tensor([False], device='cuda:3')
Visual Anomaly: tensor([True], device='cuda:3')
Audio Anomaly: tensor([True], device='cuda:3')
Full Modal Confidence: tensor([0.5000], device='cuda:3', grad_fn=<MeanBackward1>)
Visual Only Confidence: tensor([1.], device='cuda:3', dtype=torch.bfloat16, grad_fn=<StackBackward0>)
Audio Only Confidence: tensor([1.], device='cuda:3', dtype=torch.bfloat16, grad_fn=<StackBackward0>)
------------- 12-18-09-44-425498 Length Reward Debug -------------
Difficulty Score: tensor([0], device='cuda:2', dtype=torch.int32) (0=Easy, 1=Hard)
Problem Type: Easy
Modal Conflict: tensor([False], device='cuda:2')
Visual Anomaly: tensor([False], device='cuda:2')
Audio Anomaly: tensor([False], device='cuda:2')
Full Modal Confidence: tensor([1.], device='cuda:2', dtype=torch.bfloat16, grad_fn=<MeanBackward1>)
Visual Only Confidence: tensor([1.], device='cuda:2', dtype=torch.bfloat16, grad_fn=<StackBackward0>)
Audio Only Confidence: tensor([1.], device='cuda:2', dtype=torch.bfloat16, grad_fn=<StackBackward0>)
------------- 12-18-09-44-425209 Length Reward Debug -------------
Difficulty Score: tensor([1], device='cuda:0', dtype=torch.int32) (0=Easy, 1=Hard)
Problem Type: Hard
Modal Conflict: tensor([True], device='cuda:0')
Visual Anomaly: tensor([False], device='cuda:0')
Audio Anomaly: tensor([True], device='cuda:0')
Full Modal Confidence: tensor([0.5000], device='cuda:0', grad_fn=<MeanBackward1>)
Visual Only Confidence: tensor([0.], device='cuda:0')
Audio Only Confidence: tensor([1.], device='cuda:0', dtype=torch.bfloat16, grad_fn=<StackBackward0>)
